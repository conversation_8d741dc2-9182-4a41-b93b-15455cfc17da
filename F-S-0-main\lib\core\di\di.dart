import 'package:get_it/get_it.dart';
import 'package:dio/dio.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/dio_consumer.dart';

import 'package:pro/cubit/add-supporter-cubit/add_supporter_cubit.dart';
import 'package:pro/cubit/adult-type-cubit/adult_type_cubit.dart';
import 'package:pro/cubit/forgetCubit/forget_password_cubit.dart';
import 'package:pro/cubit/otp/otp_cubit.dart';
import 'package:pro/cubit/otp_check/otp_check_cubit.dart';
import 'package:pro/cubit/resetpassCubit/reset_password_cubit.dart';
import 'package:pro/cubit/sos/sos_cubit.dart';
import 'package:pro/cubit/traveler/traveler_cubit.dart';
import 'package:pro/cubit/user_cubit.dart';

import 'package:pro/repo/SosRepository.dart';
import 'package:pro/repo/SupporterRepository.dart';
import 'package:pro/repo/TravelerRepository.dart';
import 'package:pro/repo/UserRepository.dart';
import 'package:pro/repo/adult_type_repository.dart';
import 'package:pro/repo/forgetrepo.dart';
import 'package:pro/repo/otp_check_repo.dart';
import 'package:pro/repo/otp_repo.dart';
import 'package:pro/repo/reset_password_repo.dart';

import 'package:pro/services/notification_service.dart';
import 'package:pro/services/signalr_service.dart';

final GetIt getIt = GetIt.instance;

void setupServiceLocator() {
  // Dio
  getIt.registerLazySingleton<Dio>(() => Dio());
  getIt.registerLazySingleton(() => DioConsumer(dio: getIt<Dio>()));

  // CacheHelper
  getIt.registerLazySingleton<CacheHelper>(() => CacheHelper());

  // User
  getIt.registerLazySingleton<UserRepository>(
      () => UserRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(() => UserCubit(getIt<UserRepository>()));

  // OTP
  getIt.registerLazySingleton<OtpRepository>(
      () => OtpRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(() => OtpCubit(getIt<OtpRepository>()));

  // OTP Check
  getIt.registerLazySingleton<OtpCheckRepository>(
      () => OtpCheckRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(() => OtpCheckCubit(getIt<OtpCheckRepository>()));

  // Forget Password
  getIt.registerLazySingleton<ForgetRepo>(
      () => ForgetRepo(api: getIt<DioConsumer>()));
  getIt.registerFactory(() => ForgetPasswordCubit(getIt<ForgetRepo>()));

  // Reset Password
  getIt.registerLazySingleton<ResetPasswordRepository>(
      () => ResetPasswordRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(
      () => ResetPasswordCubit(getIt<ResetPasswordRepository>()));

  // Adult Type
  getIt.registerLazySingleton(() => AdultTypeRepository());
  getIt.registerFactory(() => AdultTypeCubit(getIt<AdultTypeRepository>()));

  // Supporter
  getIt.registerLazySingleton(
      () => SupporterRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(() => AddSupporterCubit(getIt<SupporterRepository>()));

  // Traveler
  getIt.registerLazySingleton(
      () => TravelerRepository(api: getIt<DioConsumer>()));
  getIt.registerFactory(
      () => TravelerCubit(travelerRepository: getIt<TravelerRepository>()));

  // Notification & SignalR
  getIt.registerLazySingleton(() => NotificationService());
  getIt.registerLazySingleton(() => SignalRService());

  // SOS
  getIt.registerLazySingleton(() => SosRepository(
        api: getIt<DioConsumer>(),
        signalRService: getIt<SignalRService>(),
      ));
  getIt.registerFactory(() => SosCubit(sosRepository: getIt<SosRepository>()));
}
