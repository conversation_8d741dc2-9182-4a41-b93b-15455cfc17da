import 'package:flutter/material.dart';
import 'package:pro/home/<USER>/kid_profile.dart';
import 'package:pro/home/<USER>/kid_settings.dart';
import 'package:pro/home/<USER>/parent_save.dart';
import 'package:pro/home/<USER>/timer_kid.dart';
import 'package:pro/services/kid_sos_service.dart';
import 'dart:developer';

class SafetyScreen extends StatefulWidget {
  @override
  _SafetyScreenState createState() => _SafetyScreenState();
}

class _SafetyScreenState extends State<SafetyScreen> {
  // 🚨 Send SOS to all trusted supporters
  Future<void> _sendSOSAlert() async {
    try {
      log("🚨 KID MODE: Sending SOS alert");

      // Show loading dialog
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 20),
              Text('Sending SOS...'),
            ],
          ),
        ),
      );

      // Send SOS
      final success = await KidSOSService().sendSOSToTrustedSupporters();

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show result
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success
                ? '🚨 SOS sent to all trusted supporters!'
                : '❌ Failed to send SOS. Please try again.'),
            backgroundColor: success ? Colors.red : Colors.grey,
            duration: const Duration(seconds: 4),
          ),
        );
      }

      // Navigate to ParentSave if successful
      if (success && mounted) {
        Navigator.push(context, MaterialPageRoute(builder: (context) {
          return ParentSave();
        }));
      }

      log(success ? "✅ SOS sent successfully" : "❌ SOS failed");
    } catch (e) {
      // Close loading dialog if still open
      if (mounted) Navigator.of(context).pop();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error sending SOS: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 4),
          ),
        );
      }
      log("❌ Error sending SOS: $e");
    }
  }

  void _showConfirmationDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          content: const SizedBox(
            width: 508,
            height: 144,
            child: Center(
              child: Text(
                "Are You Sure You Want to send SOS message?\n\nThis will alert all your trusted supporters with your location.",
                style: TextStyle(fontSize: 18),
                textAlign: TextAlign.center,
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () async {
                Navigator.of(context).pop(); // Close dialog first
                await _sendSOSAlert(); // Then send SOS
              },
              child: const Text("Yes",
                  style: TextStyle(
                      color: Colors.red, fontWeight: FontWeight.bold)),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text("No", style: TextStyle(color: Colors.grey)),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [Color(0xFF30C988), Color(0xFF37A3B6)],
              ),
            ),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.03,
            right: MediaQuery.of(context).size.width * 0.00008,
            child:
                Image.asset("assets/images/sun.png", width: 200, height: 200),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.22,
            left: MediaQuery.of(context).size.width * 0.01,
            child:
                Image.asset("assets/images/union.png", width: 80, height: 52),
          ),
          Positioned(
            top: MediaQuery.of(context).size.height * 0.18,
            right: MediaQuery.of(context).size.width * 0.01,
            child:
                Image.asset("assets/images/union.png", width: 80, height: 52),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Column(
              children: [
                SizedBox(
                  height: MediaQuery.of(context).size.height * 0.07,
                ),
                const Text(
                  "Hello,Sarah ",
                  style: TextStyle(
                      fontSize: 30,
                      fontWeight: FontWeight.bold,
                      color: Colors.white),
                  textAlign: TextAlign.center,
                ),
                const Text(
                  "I'm here to Keep you safe!",
                  style: TextStyle(fontSize: 18, color: Colors.white),
                ),
                Image.asset("assets/images/girl.png",
                    width: MediaQuery.of(context).size.width * 0.8,
                    height: MediaQuery.of(context).size.width * 1),
                ElevatedButton(
                  onPressed: () {
                    Navigator.push(context,
                        MaterialPageRoute(builder: (context) {
                      return const TimerWidgetKid();
                    }));
                  },
                  style: ElevatedButton.styleFrom(
                    fixedSize:
                        Size(MediaQuery.of(context).size.width * 0.8, 80),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(50),
                      side: const BorderSide(color: Colors.black, width: 2),
                    ),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    shadowColor: Colors.black,
                    padding: EdgeInsets.zero,
                  ),
                  child: Ink(
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [Color(0xFF41DB8E), Color(0xFF025F68)],
                      ),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Container(
                      alignment: Alignment.center,
                      child: const Text(
                        "Safety Mode",
                        style: TextStyle(fontSize: 24, color: Colors.white),
                      ),
                    ),
                  ),
                ),
                SizedBox(height: MediaQuery.of(context).size.height * 0.03),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _buildIconButton(
                        Icons.settings, const Color.fromRGBO(255, 181, 1, 1),
                        () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return KidSettingsScreen();
                      }));
                    }),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.02),
                    GestureDetector(
                      onTap: _showConfirmationDialog,
                      child: const CircleAvatar(
                        radius: 50,
                        backgroundColor: Color.fromRGBO(254, 122, 128, 1),
                        child: Text(
                          "SOS",
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Colors.white),
                        ),
                      ),
                    ),
                    SizedBox(width: MediaQuery.of(context).size.width * 0.02),
                    _buildIconButton(
                        Icons.person, const Color.fromRGBO(255, 181, 1, 1), () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) {
                        return KidProfileScreen();
                      }));
                    }),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildIconButton(IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: CircleAvatar(
        radius: 50,
        backgroundColor: color,
        child: Icon(icon, size: 40, color: Colors.white),
      ),
    );
  }
}
