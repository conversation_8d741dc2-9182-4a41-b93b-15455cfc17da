import 'package:flutter/material.dart';
import 'dart:convert';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/repo/SupporterRepository.dart';
import 'package:get_it/get_it.dart';

class TrustedContactsScreen extends StatefulWidget {
  const TrustedContactsScreen({Key? key}) : super(key: key);

  @override
  State<TrustedContactsScreen> createState() => _TrustedContactsScreenState();
}

class _TrustedContactsScreenState extends State<TrustedContactsScreen> {
  List<Map<String, dynamic>> supporters = [];
  bool showInput = false;
  bool isLoading = false;
  final TextEditingController _controller = TextEditingController();
  late final SupporterRepository supporterRepo;

  @override
  void initState() {
    super.initState();
    supporterRepo = GetIt.instance<SupporterRepository>();
    loadSupporters();
  }

  // Get the current user ID from cache
  String? getUserId() {
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      log("WARNING: Could not get user ID from cache");
      return null;
    }

    return userId.toString();
  }

  // Get the storage key for the current user's supporters
  String getSupportersKey() {
    final userId = getUserId();
    return userId != null ? 'supporters_$userId' : 'supporters_default';
  }

  // Load supporters from local storage
  Future<void> loadSupporters() async {
    final prefs = await SharedPreferences.getInstance();
    final String supportersKey = getSupportersKey();

    final String? data = prefs.getString(supportersKey);
    if (data != null) {
      final List<dynamic> decoded = jsonDecode(data);
      setState(() {
        supporters = decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      });
      log("Loaded ${supporters.length} supporters for user");
    } else {
      // Default supporters for new users
      supporters = [
        {'name': 'Mum', 'username': '<EMAIL>', 'selected': false},
        {'name': 'Dad', 'username': '<EMAIL>', 'selected': false},
      ];
      log("Created default supporters list for new user");
      await saveSupporters();
    }
  }

  // Save supporters to local storage
  Future<void> saveSupporters() async {
    final prefs = await SharedPreferences.getInstance();
    final String supportersKey = getSupportersKey();
    final String encoded = jsonEncode(supporters);
    await prefs.setString(supportersKey, encoded);
    log("Saved ${supporters.length} supporters with key: $supportersKey");
  }

  // Add supporter via API
  Future<void> addSupporterFromApi(String emailOrUsername) async {
    if (emailOrUsername.trim().isEmpty) return;

    setState(() {
      isLoading = true;
    });

    try {
      log("Adding supporter via API: $emailOrUsername");

      // Call the API to add the supporter
      final response = await supporterRepo.addSupporter(emailOrUsername);

      // Use the supporter name from the response if available, otherwise use the email/username
      final displayName = response.supporterName ?? emailOrUsername.trim();

      final newSupporter = {
        'name': displayName,
        'username': emailOrUsername.trim(),
        'selected': false,
        'id': response.supporterId ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        'success': response.success
      };

      setState(() {
        supporters.add(newSupporter);
        showInput = false;
        _controller.clear();
        isLoading = false;
      });

      await saveSupporters();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add supporter: $e'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF3BE489), Color(0xFF00C2E0)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: const Icon(
                        Icons.arrow_back_ios,
                        size: 14,
                        color: Colors.black,
                      ),
                    ),
                    const Expanded(
                      child: Center(
                        child: Text(
                          "Trusted contacts",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 20,
              ),
              // Add supporter button
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: Row(
                  children: [
                    Expanded(
                      child: showInput
                          ? Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    controller: _controller,
                                    decoration: const InputDecoration(
                                      hintText: 'Enter username or email',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                          horizontal: 12, vertical: 8),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                ElevatedButton(
                                  onPressed: isLoading
                                      ? null
                                      : () =>
                                          addSupporterFromApi(_controller.text),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: Colors.green,
                                  ),
                                  child: isLoading
                                      ? const SizedBox(
                                          width: 16,
                                          height: 16,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white,
                                          ),
                                        )
                                      : const Text('Add',
                                          style:
                                              TextStyle(color: Colors.white)),
                                ),
                                IconButton(
                                  onPressed: () {
                                    setState(() {
                                      showInput = false;
                                      _controller.clear();
                                    });
                                  },
                                  icon: const Icon(Icons.close),
                                ),
                              ],
                            )
                          : ElevatedButton.icon(
                              onPressed: () {
                                setState(() {
                                  showInput = true;
                                });
                              },
                              icon: const Icon(Icons.person_add,
                                  color: Colors.white),
                              label: const Text('Add Supporter',
                                  style: TextStyle(color: Colors.white)),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: const Color(0xff193869),
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 12),
                              ),
                            ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: supporters.length,
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1,
                    ),
                    itemBuilder: (context, index) {
                      return buildContactItem(supporters[index]);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Remove supporter
  Future<void> removeSupporter(int index) async {
    setState(() {
      supporters.removeAt(index);
    });
    await saveSupporters();

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Supporter removed'),
          backgroundColor: Colors.orange,
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  Widget buildContactItem(Map<String, dynamic> contact) {
    final index = supporters.indexOf(contact);

    return GestureDetector(
      onLongPress: () {
        // Show confirmation dialog for removal
        showDialog(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Remove Supporter'),
              content:
                  Text('Are you sure you want to remove ${contact["name"]}?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                    removeSupporter(index);
                  },
                  child:
                      const Text('Remove', style: TextStyle(color: Colors.red)),
                ),
              ],
            );
          },
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(15),
          boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 5)],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Person icon instead of image
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xff193869).withOpacity(0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.person,
                size: 50,
                color: Color(0xff193869),
              ),
            ),
            const SizedBox(height: 12),
            // Name
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                contact["name"] ?? "Unknown",
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Color(0xff193869),
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 4),
            // Username/Email
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                contact["username"] ?? "",
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 8),
          ],
        ),
      ),
    );
  }
}
