import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:pro/repo/SupporterRepository.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:get_it/get_it.dart';

class TrustedContactsScreen extends StatefulWidget {
  const TrustedContactsScreen({Key? key}) : super(key: key);

  @override
  State<TrustedContactsScreen> createState() => _TrustedContactsScreenState();
}

class _TrustedContactsScreenState extends State<TrustedContactsScreen> {
  List<Map<String, dynamic>> supporters = [];
  bool showInput = false;
  final TextEditingController _controller = TextEditingController();
  late final SupporterRepository supporterRepo;

  @override
  void initState() {
    super.initState();
    supporterRepo = GetIt.instance<SupporterRepository>();
    loadSupporters();
  }

  // Get the current user ID from cache
  String? getUserId() {
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      log("WARNING: Could not get user ID from cache");
      return null;
    }

    return userId.toString();
  }

  // Get the storage key for the current user's supporters
  String getSupportersKey() {
    final userId = getUserId();
    return userId != null ? 'supporters_$userId' : 'supporters_default';
  }

  Future<void> loadSupporters() async {
    final prefs = await SharedPreferences.getInstance();
    final String supportersKey = getSupportersKey();
    log("Loading supporters for key: $supportersKey");

    final String? data = prefs.getString(supportersKey);
    if (data != null) {
      final List<dynamic> decoded = jsonDecode(data);
      setState(() {
        supporters = decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      });
      log("Loaded ${supporters.length} supporters for user");
    } else {
      // Default supporters for new users
      supporters = [
        {"name": "Mum", "image": "assets/images/mum1.png", "selected": false},
        {"name": "Dad", "image": "assets/images/dad1.png", "selected": false},
      ];
      log("Created default supporters list for new user");
      await saveSupporters();
    }
  }

  Future<void> saveSupporters() async {
    final prefs = await SharedPreferences.getInstance();
    final String supportersKey = getSupportersKey();
    final String encoded = jsonEncode(supporters);
    await prefs.setString(supportersKey, encoded);
    log("Saved ${supporters.length} supporters with key: $supportersKey");
  }

  Future<void> addSupporterFromApi(String emailOrUsername) async {
    if (emailOrUsername.trim().isEmpty) return;

    try {
      log("Adding supporter via API: $emailOrUsername");

      // Call the API to add the supporter
      final response = await supporterRepo.addSupporter(emailOrUsername);

      // Use the supporter name from the response if available, otherwise use the email/username
      final displayName = response.supporterName ?? emailOrUsername.trim();

      final newSupporter = {
        'name': displayName,
        'image': 'assets/images/mum1.png', // Default image
        'selected': false,
        'id': response.supporterId ??
            DateTime.now().millisecondsSinceEpoch.toString(),
        'email': emailOrUsername.trim(),
        'success': response.success
      };

      setState(() {
        supporters.add(newSupporter);
        showInput = false;
        _controller.clear();
      });

      await saveSupporters();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(response.message),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        // Extract the error message from the exception
        String errorMessage = e.toString();
        if (errorMessage.contains('Exception: ')) {
          errorMessage = errorMessage.split('Exception: ')[1];
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add supporter: $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
          ),
        );

        log("Error adding supporter: $e");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF3BE489), Color(0xFF00C2E0)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(
                height: 20,
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 15, horizontal: 16),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: const Icon(
                        Icons.arrow_back_ios,
                        size: 14,
                        color: Colors.black,
                      ),
                    ),
                    const Expanded(
                      child: Center(
                        child: Text(
                          "Trusted contacts",
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.black,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              // Add supporter input field (if showInput is true)
              if (showInput)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.9),
                      borderRadius: BorderRadius.circular(15),
                      boxShadow: const [
                        BoxShadow(color: Colors.black12, blurRadius: 5)
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            controller: _controller,
                            decoration: const InputDecoration(
                              hintText: "Enter email or username",
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                          ),
                        ),
                        const SizedBox(width: 10),
                        ElevatedButton(
                          onPressed: () =>
                              addSupporterFromApi(_controller.text),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                          ),
                          child: const Text("Add",
                              style: TextStyle(color: Colors.white)),
                        ),
                        IconButton(
                          onPressed: () {
                            setState(() {
                              showInput = false;
                              _controller.clear();
                            });
                          },
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),
                  ),
                ),
              const SizedBox(
                height: 20,
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: supporters.length + 1, // +1 for the add button
                    gridDelegate:
                        const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2,
                      crossAxisSpacing: 12,
                      mainAxisSpacing: 12,
                      childAspectRatio: 1,
                    ),
                    itemBuilder: (context, index) {
                      // Add button as the last item
                      if (index == supporters.length) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              showInput = !showInput;
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(15),
                              boxShadow: const [
                                BoxShadow(color: Colors.black12, blurRadius: 5)
                              ],
                              border: Border.all(
                                  color: Colors.grey.withOpacity(0.3),
                                  width: 2),
                            ),
                            child: const Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.person_add,
                                  size: 50,
                                  color: Color(0xff193869),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  "Add Supporter",
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.bold,
                                    color: Color(0xff193869),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      return buildContactItem(supporters[index]);
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildContactItem(Map<String, dynamic> contact) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(15),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 5)],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 5),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(10),
              child: Padding(
                padding: const EdgeInsets.all(5.0),
                child: contact["image"] != null
                    ? Image.asset(
                        contact["image"]!,
                        width: double.infinity,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        width: double.infinity,
                        height: 80,
                        decoration: BoxDecoration(
                          color: const Color(0xff193869).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Icon(
                          Icons.person,
                          size: 50,
                          color: Color(0xff193869),
                        ),
                      ),
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.only(bottom: 30.0),
            child: Text(
              contact["name"] ?? "Unknown",
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
