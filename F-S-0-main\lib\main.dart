import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/cubit/sos/sos_cubit.dart';
import 'package:pro/cubit/user_cubit.dart';
import 'package:pro/login/logo_splash.dart';
import 'package:pro/repo/UserRepository.dart';
import 'package:pro/core/di/di.dart';
import 'package:pro/services/notification_service.dart';
import 'package:pro/services/signalr_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await CacheHelper.init();
  setupServiceLocator();

  await _extractUserInfoFromTokenOnStartup();

  // ✅ إشعارات الجهاز
  await getIt<NotificationService>().initialize();

  // ✅ الاتصال بـ SignalR
  await getIt<SignalRService>().startConnection();

  // ✅ استقبال إشعارات SOS
  getIt<SignalRService>().sosNotifications.listen((notification) {
    getIt<NotificationService>().showSosNotification(notification);
  });

  runApp(
    MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) =>
              UserCubit(UserRepository(api: DioConsumer(dio: Dio()))),
        ),
        BlocProvider(
          create: (context) => getIt<SosCubit>()..initialize(),
        ),
      ],
      child: MyApp(),
    ),
  );
}

Future<void> _extractUserInfoFromTokenOnStartup() async {
  try {
    final token = CacheHelper.getData(key: ApiKey.token);
    if (token == null || token.toString().isEmpty) {
      print("Debug - No token found on startup");
      return;
    }

    final decodedToken = JwtDecoder.decode(token.toString());
    print("Debug - Decoded token: $decodedToken");

    final possibleNameClaims = [
      'name',
      'fullname',
      'given_name',
      'family_name',
      'preferred_username',
      'username',
      'unique_name',
      'display_name',
    ];

    for (var claim in possibleNameClaims) {
      if (decodedToken[claim]?.toString().isNotEmpty ?? false) {
        await CacheHelper.saveData(
            key: ApiKey.name, value: decodedToken[claim]);
        break;
      }
    }

    final possibleEmailClaims = ['email', 'email_address', 'mail', 'upn'];
    for (var claim in possibleEmailClaims) {
      if (decodedToken[claim]?.toString().isNotEmpty ?? false) {
        await CacheHelper.saveData(
            key: ApiKey.email, value: decodedToken[claim]);
        break;
      }
    }
  } catch (e) {
    print("Error extracting user info from token: $e");
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});
  @override
  Widget build(BuildContext context) {
    return const MaterialApp(
      debugShowCheckedModeBanner: false,
      home: LogoSplash(),
    );
  }
}