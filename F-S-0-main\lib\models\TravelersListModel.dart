class TravelersListModel {
  final bool success;
  final String message;
  final List<TravelerItem> travelers;

  TravelersListModel({
    required this.success,
    required this.message,
    required this.travelers,
  });

  factory TravelersListModel.fromJson(Map<String, dynamic> json) {
    List<TravelerItem> travelersList = [];
    
    if (json.contains<PERSON>ey('travelers') && json['travelers'] is List) {
      travelersList = List<TravelerItem>.from(
        json['travelers'].map((traveler) => TravelerItem.fromJson(traveler))
      );
    }
    
    return TravelersListModel(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      travelers: travelersList,
    );
  }
}

class TravelerItem {
  final String id;
  final String name;
  final String email;
  final String? profilePicture;
  final String? supporterId;
  final String? supporterName;

  TravelerItem({
    required this.id,
    required this.name,
    required this.email,
    this.profilePicture,
    this.supporterId,
    this.supporterName,
  });

  factory TravelerItem.fromJson(Map<String, dynamic> json) {
    return TravelerItem(
      id: json['id'] ?? json['travelerId'] ?? '',
      name: json['name'] ?? json['travelerName'] ?? json['fullName'] ?? '',
      email: json['email'] ?? '',
      profilePicture: json['profilePicture'] ?? json['profilePic'],
      supporterId: json['supporterId'],
      supporterName: json['supporterName'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'profilePicture': profilePicture,
      'supporterId': supporterId,
      'supporterName': supporterName,
    };
  }
}
