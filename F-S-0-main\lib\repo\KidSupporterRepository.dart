import 'dart:developer';
import 'package:pro/core/API/dio_consumer.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/models/KidAddSupporterModel.dart';

class KidSupporterRepository {
  final DioConsumer api;

  KidSupporterRepository({required this.api});

  Future<KidAddSupporterModel> addSupporterToKid(String emailOrUsername) async {
    try {
      log("KidSupporterRepository: Adding supporter to kid - $emailOrUsername");

      final response = await api.post(
        EndPoint.kidTrustedContacts,
        data: {
          "EmailOrUsername": emailOrUsername,
        },
      );

      log("KidSupporterRepository: Response received - ${response.data}");

      if (response.statusCode == 200) {
        final model = KidAddSupporterModel.fromJson(response.data);
        log("KidSupporterRepository: Successfully parsed response - ${model.message}");
        return model;
      } else {
        log("KidSupporterRepository: Unexpected status code - ${response.statusCode}");
        throw Exception(
            'Failed to add supporter. Status: ${response.statusCode}');
      }
    } catch (e) {
      log("KidSupporterRepository: Error occurred - $e");

      if (e.toString().contains('DioException')) {
        // Handle specific Dio errors
        if (e.toString().contains('404')) {
          throw Exception('User not found');
        } else if (e.toString().contains('400')) {
          throw Exception('Invalid email or username');
        } else if (e.toString().contains('401')) {
          throw Exception('Unauthorized. Please log in again');
        } else if (e.toString().contains('500')) {
          throw Exception('Server error. Please try again later');
        }
      }

      // Re-throw the original exception if it's already formatted
      if (e is Exception) {
        rethrow;
      }

      // Wrap other errors
      throw Exception('Failed to add supporter: ${e.toString()}');
    }
  }

  Future<List<Map<String, dynamic>>> getKidSupporters() async {
    try {
      log("KidSupporterRepository: Getting kid supporters");

      final response = await api.get(EndPoint.kidSupportersMine);

      log("KidSupporterRepository: Supporters response received - ${response.data}");

      if (response.statusCode == 200) {
        if (response.data is List) {
          return List<Map<String, dynamic>>.from(response.data);
        } else if (response.data is Map &&
            response.data['supporters'] != null) {
          return List<Map<String, dynamic>>.from(response.data['supporters']);
        } else {
          log("KidSupporterRepository: Unexpected response format");
          return [];
        }
      } else {
        log("KidSupporterRepository: Failed to get supporters - ${response.statusCode}");
        throw Exception(
            'Failed to get supporters. Status: ${response.statusCode}');
      }
    } catch (e) {
      log("KidSupporterRepository: Error getting supporters - $e");

      // For getting supporters, we can return empty list on error
      // This allows the app to continue working with local data
      return [];
    }
  }

  Future<bool> removeSupporterFromKid(String supporterId) async {
    try {
      log("KidSupporterRepository: Removing supporter from kid - $supporterId");

      final response = await api.delete(
        "${EndPoint.kidTrustedContacts}/$supporterId",
      );

      log("KidSupporterRepository: Remove response received - ${response.data}");

      if (response.statusCode == 200) {
        return true;
      } else {
        log("KidSupporterRepository: Failed to remove supporter - ${response.statusCode}");
        return false;
      }
    } catch (e) {
      log("KidSupporterRepository: Error removing supporter - $e");
      return false;
    }
  }
}
