import 'dart:developer';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/core/API/api_consumer.dart';
import 'package:pro/models/AddSupporterModel.dart';
import 'package:pro/models/SupportersListModel.dart';

class SupporterRepository {
  final ApiConsumer api;

  SupporterRepository({required this.api});

  // Get the current user ID from cache
  String? getUserId() {
    // Try to get user ID from different sources
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      log("WARNING: Could not get user ID from cache");
      return null;
    }

    return userId.toString();
  }

  Future<AddSupporterModel> addSupporter(String emailOrUsername) async {
    // Get the current user ID (traveler) from cache
    final travelerId = getUserId();

    if (travelerId == null || travelerId.toString().isEmpty) {
      throw Exception("User ID not found. Please log in again.");
    }

    // Check if token exists
    final token = CacheHelper.getData(key: ApiKey.token);
    if (token == null || token.toString().isEmpty) {
      throw Exception("Authentication token not found. Please log in again.");
    }

    try {
      log("Adding supporter with email/username: $emailOrUsername by traveler ID: $travelerId");

      // The ApiInterceptor will automatically add the Authorization header
      final response = await api.post(
        EndPoint.addSupporter,
        data: {
          "EmailOrUsername": emailOrUsername,
          "TravelerId":
              travelerId, // Send the traveler ID to create the relationship
        },
      );

      log("Supporter API response: $response");
      return AddSupporterModel.fromJson(response);
    } catch (e) {
      log("Error adding supporter: $e");
      throw Exception("Failed to add supporter: $e");
    }
  }

  // Get supporter's list
  Future<SupportersListModel> getSupportersList() async {
    // Get the current user ID from cache
    final userId = getUserId();

    if (userId == null || userId.toString().isEmpty) {
      throw Exception("User ID not found. Please log in again.");
    }

    // Check if token exists
    final token = CacheHelper.getData(key: ApiKey.token);
    if (token == null || token.toString().isEmpty) {
      throw Exception("Authentication token not found. Please log in again.");
    }

    try {
      log("Getting supporters list for user ID: $userId");

      final response = await api.get(
        EndPoint.getSupportersList,
      );

      log("Supporters list API response: $response");

      if (response is List) {
        // If response is a list, convert it to the expected format
        return SupportersListModel(
          success: true,
          message: "Supporters list retrieved successfully",
          supporters: List<SupporterItem>.from(
              response.map((supporter) => SupporterItem.fromJson(supporter))),
        );
      } else if (response is Map<String, dynamic>) {
        // If response is a map, use the fromJson constructor
        return SupportersListModel.fromJson(response);
      }

      // If response format is unexpected, return empty list
      return SupportersListModel(
        success: false,
        message: "Invalid response format",
        supporters: [],
      );
    } catch (e) {
      log("Error getting supporters list: $e");
      throw Exception("Failed to get supporters list: $e");
    }
  }
}
