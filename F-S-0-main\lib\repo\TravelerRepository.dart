import 'dart:developer';
import 'dart:convert';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/core/API/api_consumer.dart';
import 'package:pro/models/AddTravelerModel.dart';
import 'package:pro/models/TravelersListModel.dart';

class TravelerRepository {
  final ApiConsumer api;

  TravelerRepository({required this.api});

  // Get the current user ID from cache
  String? getUserId() {
    // Try to get user ID from different sources
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      log("WARNING: Could not get user ID from cache");
      return null;
    }

    return userId.toString();
  }

  Future<AddTravelerModel> addTraveler(String emailOrUsername) async {
    // Get the current user ID (supporter) from cache
    final supporterId = getUserId();

    if (supporterId == null || supporterId.toString().isEmpty) {
      throw Exception("User ID not found. Please log in again.");
    }

    // Check if token exists
    final token = CacheHelper.getData(key: ApiKey.token);
    if (token == null || token.toString().isEmpty) {
      throw Exception("Authentication token not found. Please log in again.");
    }

    try {
      log("Adding traveler with email/username: $emailOrUsername by supporter ID: $supporterId");

      // Send both the traveler email/username and the current user ID (supporter)
      final response = await api.post(
        EndPoint.addTraveler,
        data: {
          'EmailOrUsername': emailOrUsername,
          'SupporterId':
              supporterId, // Send the supporter ID to create the relationship
        },
      );

      log("Traveler API response: $response");
      return AddTravelerModel.fromJson(response);
    } catch (e) {
      log("Error adding traveler: $e");
      throw Exception("Failed to add traveler: $e");
    }
  }

  // Get traveler's list
  Future<TravelersListModel> getTravelersList() async {
    // Get the current user ID from cache
    final userId = getUserId();

    if (userId == null || userId.toString().isEmpty) {
      throw Exception("User ID not found. Please log in again.");
    }

    // Check if token exists
    final token = CacheHelper.getData(key: ApiKey.token);
    if (token == null || token.toString().isEmpty) {
      throw Exception("Authentication token not found. Please log in again.");
    }

    try {
      log("Getting travelers list for user ID: $userId");

      final response = await api.get(
        EndPoint.getTravelersList,
      );

      log("Travelers list API response: $response");

      if (response is List) {
        // If response is a list, convert it to the expected format
        return TravelersListModel(
          success: true,
          message: "Travelers list retrieved successfully",
          travelers: List<TravelerItem>.from(
              response.map((traveler) => TravelerItem.fromJson(traveler))),
        );
      } else if (response is Map<String, dynamic>) {
        // If response is a map, use the fromJson constructor
        return TravelersListModel.fromJson(response);
      }

      // If response format is unexpected, return empty list
      return TravelersListModel(
        success: false,
        message: "Invalid response format",
        travelers: [],
      );
    } catch (e) {
      log("Error getting travelers list: $e");
      throw Exception("Failed to get travelers list: $e");
    }
  }
}
