import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/models/start_trip_model.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'dart:convert';
import 'dart:developer';

import '../core/API/dio_consumer.dart';

class StartTripRepository {
  final DioConsumer api;

  StartTripRepository({required this.api});

  Future<StartTripResponseModel> startTrip({
    required double latitude,
    required double longitude,
    bool isKidMode = false,
  }) async {
    // Get user ID
    final userId = _getUserId();
    if (userId == null) {
      throw Exception("User ID not found. Please log in again.");
    }

    final data = {
      'userId': userId,
      'startLatitude': latitude,
      'startLongitude': longitude,
      'isKidMode': isKidMode,
      'startTime': DateTime.now().toIso8601String(),
    };

    log("🚗 Starting trip with data: $data");
    log("🚗 Using endpoint: ${EndPoint.startTrip}");

    try {
      final response = await api.post(EndPoint.startTrip, data: data);
      log("✅ Start trip API response: $response");
      return StartTripResponseModel.fromJson(response);
    } catch (e) {
      log("❌ Start trip API error: $e");

      // Extract more meaningful error message
      String errorMessage = e.toString();
      if (errorMessage.contains("TRAVELER_DISCONNECTED")) {
        errorMessage =
            "Traveler must be connected to supporters to start a trip. Please add supporters first.";
      } else if (errorMessage.contains("400")) {
        errorMessage =
            "Bad request: Please check if you have supporters added and are properly connected.";
      }

      throw Exception(errorMessage);
    }
  }

  // Get current user ID
  String? _getUserId() {
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      // Try to extract from token
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token != null) {
        return _extractUserIdFromToken(token.toString());
      }
      log("WARNING: Could not get user ID for start trip");
      return null;
    }

    return userId.toString();
  }

  // Extract user ID from JWT token
  String? _extractUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> claims = jsonDecode(decoded);

      final possibleKeys = [
        'sub',
        'user_id',
        'userId',
        'id',
        'nameid',
        'unique_name'
      ];
      for (String key in possibleKeys) {
        if (claims.containsKey(key)) {
          return claims[key].toString();
        }
      }
    } catch (e) {
      log("Error decoding JWT token for start trip: $e");
    }
    return null;
  }
}
