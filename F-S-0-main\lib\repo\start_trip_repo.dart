import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/models/start_trip_model.dart';

import '../core/API/dio_consumer.dart';


class StartTripRepository {
  final DioConsumer api;

  StartTripRepository({required this.api});

  Future<StartTripResponseModel> startTrip() async {
    final response = await api.post(EndPoint.startTrip);
    return StartTripResponseModel.fromJson(response);
  }
}
