import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class AppTimerService {
  static final AppTimerService _instance = AppTimerService._internal();
  factory AppTimerService() => _instance;
  AppTimerService._internal();

  Timer? _timer;
  int _seconds = 0;
  bool _isRunning = false;
  bool _isKidMode = false;
  
  // Callbacks for UI updates
  final List<Function(int seconds, bool isRunning, String formattedTime)> _listeners = [];
  
  // Callbacks for dialog triggers
  final List<Function()> _dialogListeners = [];

  final FlutterLocalNotificationsPlugin _notificationsPlugin = 
      FlutterLocalNotificationsPlugin();

  // Initialize the service
  Future<void> initialize() async {
    await _initializeNotifications();
    log("🕐 App Timer Service initialized");
  }

  // Initialize notifications
  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    
    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);
    
    await _notificationsPlugin.initialize(initializationSettings);
  }

  // Add listener for timer updates
  void addTimerListener(Function(int seconds, bool isRunning, String formattedTime) listener) {
    _listeners.add(listener);
  }

  // Remove listener
  void removeTimerListener(Function(int seconds, bool isRunning, String formattedTime) listener) {
    _listeners.remove(listener);
  }

  // Add dialog listener
  void addDialogListener(Function() listener) {
    _dialogListeners.add(listener);
  }

  // Remove dialog listener
  void removeDialogListener(Function() listener) {
    _dialogListeners.remove(listener);
  }

  // Notify all listeners
  void _notifyListeners() {
    final formattedTime = _formatTime(_seconds);
    for (var listener in _listeners) {
      try {
        listener(_seconds, _isRunning, formattedTime);
      } catch (e) {
        log("Error notifying timer listener: $e");
      }
    }
  }

  // Notify dialog listeners
  void _notifyDialogListeners() {
    for (var listener in _dialogListeners) {
      try {
        listener();
      } catch (e) {
        log("Error notifying dialog listener: $e");
      }
    }
  }

  // Start the timer
  Future<void> startTimer({bool isKidMode = false}) async {
    if (_isRunning) return;
    
    _isKidMode = isKidMode;
    _isRunning = true;
    
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _seconds++;
      _notifyListeners();
      
      // Check if we need to show dialog (every 10 seconds)
      if (_seconds % 10 == 0) {
        _showTimerNotification();
        _notifyDialogListeners();
      }
      
      log("🕐 Timer tick: ${_seconds}s (Kid Mode: $_isKidMode)");
    });
    
    _notifyListeners();
    log("🕐 Timer started (Kid Mode: $isKidMode)");
  }

  // Stop the timer
  Future<void> stopTimer() async {
    _isRunning = false;
    _timer?.cancel();
    _timer = null;
    _notifyListeners();
    
    log("🕐 Timer stopped");
  }

  // Reset the timer
  Future<void> resetTimer() async {
    final wasRunning = _isRunning;
    await stopTimer();
    _seconds = 0;
    _notifyListeners();
    
    if (wasRunning) {
      await startTimer(isKidMode: _isKidMode);
    }
    
    log("🕐 Timer reset");
  }

  // Show timer notification
  Future<void> _showTimerNotification() async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'timer_alert_channel',
      'Timer Alerts',
      channelDescription: 'Safety check notifications',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: false,
    );
    
    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);
    
    await _notificationsPlugin.show(
      0,
      'Safety Check',
      _isKidMode ? 'Hey Kid, Are You ok?' : 'Hey, Are You ok?',
      platformChannelSpecifics,
    );
    
    log("🔔 Timer notification shown");
  }

  // Get current timer state
  Map<String, dynamic> getTimerState() {
    return {
      'seconds': _seconds,
      'isRunning': _isRunning,
      'isKidMode': _isKidMode,
      'formattedTime': _formatTime(_seconds),
    };
  }

  // Format time
  String _formatTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  // Get current seconds
  int get seconds => _seconds;
  
  // Get running state
  bool get isRunning => _isRunning;
  
  // Get kid mode state
  bool get isKidMode => _isKidMode;
  
  // Get formatted time
  String get formattedTime => _formatTime(_seconds);

  // Dispose resources
  void dispose() {
    _timer?.cancel();
    _listeners.clear();
    _dialogListeners.clear();
  }
}
