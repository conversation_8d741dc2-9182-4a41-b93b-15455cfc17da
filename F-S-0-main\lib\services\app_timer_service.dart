import 'dart:async';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:pro/services/trip_service.dart';
import 'package:pro/core/di/di.dart';

class AppTimerService {
  static final AppTimerService _instance = AppTimerService._internal();
  factory AppTimerService() => _instance;
  AppTimerService._internal();

  // Separate timers for kid and traveler
  Timer? _kidTimer;
  Timer? _travelerTimer;

  // Kid timer state
  int _kidSeconds = 0;
  bool _kidIsRunning = false;

  // Traveler timer state
  int _travelerSeconds = 0;
  bool _travelerIsRunning = false;

  // Callbacks for UI updates - separated by type
  final List<Function(int seconds, bool isRunning, String formattedTime)>
      _kidListeners = [];
  final List<Function(int seconds, bool isRunning, String formattedTime)>
      _travelerListeners = [];

  // Callbacks for dialog triggers - separated by type
  final List<Function()> _kidDialogListeners = [];
  final List<Function()> _travelerDialogListeners = [];

  final FlutterLocalNotificationsPlugin _notificationsPlugin =
      FlutterLocalNotificationsPlugin();

  // Initialize the service
  Future<void> initialize() async {
    await _initializeNotifications();
    log("🕐 App Timer Service initialized");
  }

  // Initialize notifications
  Future<void> _initializeNotifications() async {
    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const InitializationSettings initializationSettings =
        InitializationSettings(android: initializationSettingsAndroid);

    await _notificationsPlugin.initialize(initializationSettings);
  }

  // Add listener for kid timer updates
  void addKidTimerListener(
      Function(int seconds, bool isRunning, String formattedTime) listener) {
    _kidListeners.add(listener);
  }

  // Add listener for traveler timer updates
  void addTravelerTimerListener(
      Function(int seconds, bool isRunning, String formattedTime) listener) {
    _travelerListeners.add(listener);
  }

  // Remove kid timer listener
  void removeKidTimerListener(
      Function(int seconds, bool isRunning, String formattedTime) listener) {
    _kidListeners.remove(listener);
  }

  // Remove traveler timer listener
  void removeTravelerTimerListener(
      Function(int seconds, bool isRunning, String formattedTime) listener) {
    _travelerListeners.remove(listener);
  }

  // Add kid dialog listener
  void addKidDialogListener(Function() listener) {
    _kidDialogListeners.add(listener);
  }

  // Add traveler dialog listener
  void addTravelerDialogListener(Function() listener) {
    _travelerDialogListeners.add(listener);
  }

  // Remove kid dialog listener
  void removeKidDialogListener(Function() listener) {
    _kidDialogListeners.remove(listener);
  }

  // Remove traveler dialog listener
  void removeTravelerDialogListener(Function() listener) {
    _travelerDialogListeners.remove(listener);
  }

  // Notify kid listeners
  void _notifyKidListeners() {
    final formattedTime = _formatTime(_kidSeconds);
    for (var listener in _kidListeners) {
      try {
        listener(_kidSeconds, _kidIsRunning, formattedTime);
      } catch (e) {
        log("Error notifying kid timer listener: $e");
      }
    }
  }

  // Notify traveler listeners
  void _notifyTravelerListeners() {
    final formattedTime = _formatTime(_travelerSeconds);
    for (var listener in _travelerListeners) {
      try {
        listener(_travelerSeconds, _travelerIsRunning, formattedTime);
      } catch (e) {
        log("Error notifying traveler timer listener: $e");
      }
    }
  }

  // Notify kid dialog listeners
  void _notifyKidDialogListeners() {
    for (var listener in _kidDialogListeners) {
      try {
        listener();
      } catch (e) {
        log("Error notifying kid dialog listener: $e");
      }
    }
  }

  // Notify traveler dialog listeners
  void _notifyTravelerDialogListeners() {
    for (var listener in _travelerDialogListeners) {
      try {
        listener();
      } catch (e) {
        log("Error notifying traveler dialog listener: $e");
      }
    }
  }

  // Start kid timer
  Future<void> startKidTimer() async {
    if (_kidIsRunning) return;

    _kidIsRunning = true;

    // Start trip for kid
    try {
      final tripService = getIt<TripService>();
      final tripStarted = await tripService.startTrip(isKidMode: true);
      if (tripStarted) {
        log("🚗 Kid trip started successfully");
      } else {
        log("❌ Failed to start kid trip");
      }
    } catch (e) {
      log("❌ Error starting kid trip: $e");
    }

    _kidTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _kidSeconds++;
      _notifyKidListeners();

      // Check if we need to show dialog (every 10 seconds)
      if (_kidSeconds % 10 == 0) {
        _showTimerNotification(isKidMode: true);
        _notifyKidDialogListeners();
      }

      log("🕐 Kid Timer tick: ${_kidSeconds}s");
    });

    _notifyKidListeners();
    log("🕐 Kid Timer started");
  }

  // Start traveler timer
  Future<void> startTravelerTimer() async {
    if (_travelerIsRunning) return;

    _travelerIsRunning = true;

    // Start trip for traveler
    try {
      final tripService = getIt<TripService>();
      final tripStarted = await tripService.startTrip(isKidMode: false);
      if (tripStarted) {
        log("🚗 Traveler trip started successfully");
      } else {
        log("❌ Failed to start traveler trip");
      }
    } catch (e) {
      log("❌ Error starting traveler trip: $e");
    }

    _travelerTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _travelerSeconds++;
      _notifyTravelerListeners();

      // Check if we need to show dialog (every 10 seconds)
      if (_travelerSeconds % 10 == 0) {
        _showTimerNotification(isKidMode: false);
        _notifyTravelerDialogListeners();
      }

      log("🕐 Traveler Timer tick: ${_travelerSeconds}s");
    });

    _notifyTravelerListeners();
    log("🕐 Traveler Timer started");
  }

  // Stop kid timer
  Future<void> stopKidTimer() async {
    _kidIsRunning = false;
    _kidTimer?.cancel();
    _kidTimer = null;

    // End trip for kid
    try {
      final tripService = getIt<TripService>();
      final tripEnded = await tripService.endTrip();
      if (tripEnded) {
        log("🚗 Kid trip ended successfully");
      } else {
        log("❌ Failed to end kid trip");
      }
    } catch (e) {
      log("❌ Error ending kid trip: $e");
    }

    _notifyKidListeners();
    log("🕐 Kid Timer stopped");
  }

  // Stop traveler timer
  Future<void> stopTravelerTimer() async {
    _travelerIsRunning = false;
    _travelerTimer?.cancel();
    _travelerTimer = null;

    // End trip for traveler
    try {
      final tripService = getIt<TripService>();
      final tripEnded = await tripService.endTrip();
      if (tripEnded) {
        log("🚗 Traveler trip ended successfully");
      } else {
        log("❌ Failed to end traveler trip");
      }
    } catch (e) {
      log("❌ Error ending traveler trip: $e");
    }

    _notifyTravelerListeners();
    log("🕐 Traveler Timer stopped");
  }

  // Reset kid timer
  Future<void> resetKidTimer() async {
    final wasRunning = _kidIsRunning;
    await stopKidTimer();
    _kidSeconds = 0;
    _notifyKidListeners();

    if (wasRunning) {
      await startKidTimer();
    }

    log("🕐 Kid Timer reset");
  }

  // Reset traveler timer
  Future<void> resetTravelerTimer() async {
    final wasRunning = _travelerIsRunning;
    await stopTravelerTimer();
    _travelerSeconds = 0;
    _notifyTravelerListeners();

    if (wasRunning) {
      await startTravelerTimer();
    }

    log("🕐 Traveler Timer reset");
  }

  // Show timer notification
  Future<void> _showTimerNotification({required bool isKidMode}) async {
    const AndroidNotificationDetails androidPlatformChannelSpecifics =
        AndroidNotificationDetails(
      'timer_alert_channel',
      'Timer Alerts',
      channelDescription: 'Safety check notifications',
      importance: Importance.max,
      priority: Priority.high,
      showWhen: false,
    );

    const NotificationDetails platformChannelSpecifics =
        NotificationDetails(android: androidPlatformChannelSpecifics);

    await _notificationsPlugin.show(
      isKidMode ? 1 : 2, // Different notification IDs
      'Safety Check',
      isKidMode ? 'Hey Kid, Are You ok?' : 'Hey, Are You ok?',
      platformChannelSpecifics,
    );

    log("🔔 Timer notification shown (Kid Mode: $isKidMode)");
  }

  // Get kid timer state
  Map<String, dynamic> getKidTimerState() {
    return {
      'seconds': _kidSeconds,
      'isRunning': _kidIsRunning,
      'isKidMode': true,
      'formattedTime': _formatTime(_kidSeconds),
    };
  }

  // Get traveler timer state
  Map<String, dynamic> getTravelerTimerState() {
    return {
      'seconds': _travelerSeconds,
      'isRunning': _travelerIsRunning,
      'isKidMode': false,
      'formattedTime': _formatTime(_travelerSeconds),
    };
  }

  // Format time
  String _formatTime(int seconds) {
    int hours = seconds ~/ 3600;
    int minutes = (seconds % 3600) ~/ 60;
    int secs = seconds % 60;
    return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  // Get kid timer getters
  int get kidSeconds => _kidSeconds;
  bool get kidIsRunning => _kidIsRunning;
  String get kidFormattedTime => _formatTime(_kidSeconds);

  // Get traveler timer getters
  int get travelerSeconds => _travelerSeconds;
  bool get travelerIsRunning => _travelerIsRunning;
  String get travelerFormattedTime => _formatTime(_travelerSeconds);

  // Dispose resources
  void dispose() {
    _kidTimer?.cancel();
    _travelerTimer?.cancel();
    _kidListeners.clear();
    _travelerListeners.clear();
    _kidDialogListeners.clear();
    _travelerDialogListeners.clear();
  }
}
