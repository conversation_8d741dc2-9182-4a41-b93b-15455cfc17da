import 'dart:convert';
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/services/sync_notification_service.dart';

class AutoSyncService {
  static final AutoSyncService _instance = AutoSyncService._internal();
  factory AutoSyncService() => _instance;
  AutoSyncService._internal();

  // Get the current user ID from cache
  String? getUserId() {
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      log("WARNING: Could not get user ID from cache");
      return null;
    }

    return userId.toString();
  }

  // Auto-add supporter to traveler's list when traveler is added to supporter's list
  Future<void> autoAddSupporterToTraveler({
    required String travelerId,
    required String travelerEmail,
    required String supporterId,
    required String supporterName,
    required String supporterEmail,
  }) async {
    try {
      log("AutoSyncService: Auto-adding supporter to traveler's list");
      log("Traveler ID: $travelerId, Supporter ID: $supporterId");

      // Get traveler's supporters key
      final String supportersKey = 'supporters_$travelerId';

      final prefs = await SharedPreferences.getInstance();

      // Load existing supporters for this traveler
      List<Map<String, dynamic>> supporters = [];
      final String? data = prefs.getString(supportersKey);
      if (data != null) {
        final List<dynamic> decoded = jsonDecode(data);
        supporters = decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      }

      // Check if supporter already exists
      bool supporterExists = supporters
          .any((s) => s['id'] == supporterId || s['email'] == supporterEmail);

      if (!supporterExists) {
        // Add the supporter to traveler's list
        final newSupporter = {
          'name': supporterName,
          'image': null,
          'selected': false,
          'id': supporterId,
          'email': supporterEmail,
          'auto_added': true, // Flag to indicate this was auto-added
          'added_at': DateTime.now().toIso8601String(),
        };

        supporters.add(newSupporter);

        // Save updated supporters list
        final String encoded = jsonEncode(supporters);
        await prefs.setString(supportersKey, encoded);

        log("AutoSyncService: Successfully auto-added supporter to traveler's list");
        log("Supporter '$supporterName' added to traveler '$travelerId' supporters list");

        // 🔔 Notify UI about the new supporter
        SyncNotificationService().notifySupporterAdded(
          travelerId: travelerId,
          supporterData: newSupporter,
        );
      } else {
        log("AutoSyncService: Supporter already exists in traveler's list");
      }
    } catch (e) {
      log("AutoSyncService: Error auto-adding supporter to traveler: $e");
    }
  }

  // Auto-add traveler to supporter's list when supporter is added to traveler's list
  Future<void> autoAddTravelerToSupporter({
    required String supporterId,
    required String supporterEmail,
    required String travelerId,
    required String travelerName,
    required String travelerEmail,
  }) async {
    try {
      log("AutoSyncService: Auto-adding traveler to supporter's list");
      log("Supporter ID: $supporterId, Traveler ID: $travelerId");

      // Get supporter's travelers key
      final String travelersKey = 'travelers_$supporterId';

      final prefs = await SharedPreferences.getInstance();

      // Load existing travelers for this supporter
      List<Map<String, dynamic>> travelers = [];
      final String? data = prefs.getString(travelersKey);
      if (data != null) {
        final List<dynamic> decoded = jsonDecode(data);
        travelers = decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      }

      // Check if traveler already exists
      bool travelerExists = travelers
          .any((t) => t['id'] == travelerId || t['email'] == travelerEmail);

      if (!travelerExists) {
        // Add the traveler to supporter's list
        final newTraveler = {
          'name': travelerName,
          'image': null,
          'selected': false,
          'id': travelerId,
          'email': travelerEmail,
          'supporterId': supporterId,
          'supporterName':
              supporterEmail, // This should be the supporter's name
          'auto_added': true, // Flag to indicate this was auto-added
          'added_at': DateTime.now().toIso8601String(),
        };

        travelers.add(newTraveler);

        // Save updated travelers list
        final String encoded = jsonEncode(travelers);
        await prefs.setString(travelersKey, encoded);

        log("AutoSyncService: Successfully auto-added traveler to supporter's list");
        log("Traveler '$travelerName' added to supporter '$supporterId' travelers list");

        // 🔔 Notify UI about the new traveler
        SyncNotificationService().notifyTravelerAdded(
          supporterId: supporterId,
          travelerData: newTraveler,
        );
      } else {
        log("AutoSyncService: Traveler already exists in supporter's list");
      }
    } catch (e) {
      log("AutoSyncService: Error auto-adding traveler to supporter: $e");
    }
  }

  // Sync both ways when a new relationship is created
  Future<void> syncBidirectionalRelationship({
    required String travelerId,
    required String travelerName,
    required String travelerEmail,
    required String supporterId,
    required String supporterName,
    required String supporterEmail,
  }) async {
    log("AutoSyncService: Starting bidirectional sync");

    // Add supporter to traveler's list
    await autoAddSupporterToTraveler(
      travelerId: travelerId,
      travelerEmail: travelerEmail,
      supporterId: supporterId,
      supporterName: supporterName,
      supporterEmail: supporterEmail,
    );

    // Add traveler to supporter's list
    await autoAddTravelerToSupporter(
      supporterId: supporterId,
      supporterEmail: supporterEmail,
      travelerId: travelerId,
      travelerName: travelerName,
      travelerEmail: travelerEmail,
    );

    log("AutoSyncService: Bidirectional sync completed");
  }

  // Get all supporters for a specific traveler
  Future<List<Map<String, dynamic>>> getSupportersForTraveler(
      String travelerId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String supportersKey = 'supporters_$travelerId';

      final String? data = prefs.getString(supportersKey);
      if (data != null) {
        final List<dynamic> decoded = jsonDecode(data);
        return decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      }
      return [];
    } catch (e) {
      log("AutoSyncService: Error getting supporters for traveler: $e");
      return [];
    }
  }

  // Get all travelers for a specific supporter
  Future<List<Map<String, dynamic>>> getTravelersForSupporter(
      String supporterId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final String travelersKey = 'travelers_$supporterId';

      final String? data = prefs.getString(travelersKey);
      if (data != null) {
        final List<dynamic> decoded = jsonDecode(data);
        return decoded.map((e) => Map<String, dynamic>.from(e)).toList();
      }
      return [];
    } catch (e) {
      log("AutoSyncService: Error getting travelers for supporter: $e");
      return [];
    }
  }
}
