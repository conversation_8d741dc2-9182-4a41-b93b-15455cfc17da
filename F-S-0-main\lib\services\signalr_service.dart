import 'dart:async';
import 'dart:developer';
import 'package:signalr_netcore/signalr_client.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';
import 'package:pro/models/SosNotificationModel.dart';

class SignalRService {
  static const String hubUrl = 'https://followsafe.runasp.net/notificationHub';
  static const String sosMethodName = 'ReceiveSosNotification';

  HubConnection? _hubConnection;
  final StreamController<SosNotificationModel> _sosNotificationController =
      StreamController<SosNotificationModel>.broadcast();

  Stream<SosNotificationModel> get sosNotifications =>
      _sosNotificationController.stream;
  bool _isConnected = false;

  bool get isConnected => _isConnected;

  // Initialize and start the connection
  Future<void> startConnection() async {
    if (_hubConnection != null) {
      log('SignalR connection already exists');
      return;
    }

    try {
      final token = CacheHelper.getData(key: ApiKey.token);

      if (token == null || token.toString().isEmpty) {
        log('WARNING: No token found for SignalR connection');
        return;
      }

      // Create the connection
      _hubConnection = HubConnectionBuilder()
          .withUrl('$hubUrl?access_token=$token')
          .withAutomaticReconnect()
          .build();

      // Register the SOS notification handler
      _hubConnection!.on(sosMethodName, _handleSosNotification);

      // Start the connection
      await _hubConnection!.start();
      _isConnected = true;
      log('SignalR connection started successfully');
    } catch (e) {
      _isConnected = false;
      log('Error starting SignalR connection: $e');
      rethrow;
    }
  }

  // Handle incoming SOS notifications
  void _handleSosNotification(List<Object?>? parameters) {
    if (parameters != null && parameters.isNotEmpty) {
      try {
        final data = parameters[0] as Map<String, dynamic>;

        // Check if this notification is intended for this user
        // This is a safeguard in case the server sends notifications to all users
        final bool isForSupportersOnly = data['sendToSupportersOnly'] == true;
        final String currentUserId =
            CacheHelper.getData(key: ApiKey.userId)?.toString() ?? '';
        final List<String> supporterIds = data['supporterIds'] != null
            ? List<String>.from(data['supporterIds'])
            : [];

        // If notification is for supporters only and this user is not in the supporters list,
        // and this user is not the traveler who sent the SOS, then ignore the notification
        final String travelerId = data['travelerId']?.toString() ?? '';
        if (isForSupportersOnly &&
            !supporterIds.contains(currentUserId) &&
            travelerId != currentUserId) {
          log('Ignoring SOS notification: Not in supporters list');
          return;
        }

        final notification = SosNotificationModel.fromJson(data);
        _sosNotificationController.add(notification);
        log('Received SOS notification: ${notification.message} from ${notification.travelerName}');
      } catch (e) {
        log('Error handling SOS notification: $e');
      }
    }
  }

  // Send location update via SignalR
  Future<void> sendLocationUpdate(Map<String, dynamic> locationData) async {
    if (_hubConnection != null && _isConnected) {
      try {
        await _hubConnection!
            .invoke('SendLocationUpdate', args: [locationData]);
        log('Location update sent via SignalR');
      } catch (e) {
        log('Error sending location update via SignalR: $e');
      }
    } else {
      log('SignalR connection not available for location update');
    }
  }

  // Send generic message via SignalR
  Future<void> sendMessage(String methodName, Map<String, dynamic> data) async {
    if (_hubConnection != null && _isConnected) {
      try {
        await _hubConnection!.invoke(methodName, args: [data]);
        log('Message sent via SignalR: $methodName');
      } catch (e) {
        log('Error sending message via SignalR: $e');
      }
    } else {
      log('SignalR connection not available for message: $methodName');
    }
  }

  // Stop the connection
  Future<void> stopConnection() async {
    if (_hubConnection != null) {
      try {
        await _hubConnection!.stop();
        _isConnected = false;
        log('SignalR connection stopped');
      } catch (e) {
        log('Error stopping SignalR connection: $e');
      }
    }
  }

  // Dispose resources
  void dispose() {
    stopConnection();
    _sosNotificationController.close();
  }
}
