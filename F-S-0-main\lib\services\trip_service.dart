import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:geolocator/geolocator.dart';
import 'package:pro/cache/CacheHelper.dart';
import 'package:pro/core/API/ApiKey.dart';

import 'package:pro/services/signalr_service.dart';
import 'package:pro/cubit/startTrip/start_trip_cubit.dart';
import 'package:pro/cubit/startTrip/start_trip.state.dart';
import 'package:pro/cubit/updateLocation/updatelocation_cubit.dart';
import 'package:pro/cubit/updateLocation/updateLocation_state.dart';
import 'package:pro/cubit/end-trip/end_trip_cubit.dart';
import 'package:pro/cubit/end-trip/end_trip_state.dart';
import 'package:pro/core/di/di.dart';

class TripService {
  static final TripService _instance = TripService._internal();
  factory TripService() => _instance;
  TripService._internal();

  SignalRService? _signalRService;

  Timer? _locationTimer;
  bool _isTripActive = false;
  String? _currentTripId;
  Position? _lastKnownPosition;

  // Initialize with dependencies
  void initialize({
    required SignalRService signalRService,
  }) {
    _signalRService = signalRService;
    log("🚗 TripService initialized");
  }

  // Get current user ID
  String? _getUserId() {
    final userId = CacheHelper.getData(key: ApiKey.userId) ??
        CacheHelper.getData(key: "current_user_id") ??
        CacheHelper.getData(key: ApiKey.id) ??
        CacheHelper.getData(key: "userId") ??
        CacheHelper.getData(key: "UserId") ??
        CacheHelper.getData(key: "sub");

    if (userId == null || userId.toString().isEmpty) {
      // Try to extract from token
      final token = CacheHelper.getData(key: ApiKey.token);
      if (token != null) {
        return _extractUserIdFromToken(token.toString());
      }
      log("WARNING: Could not get user ID for trip");
      return null;
    }

    return userId.toString();
  }

  // Extract user ID from JWT token
  String? _extractUserIdFromToken(String token) {
    try {
      final parts = token.split('.');
      if (parts.length != 3) return null;

      final payload = parts[1];
      final normalized = base64.normalize(payload);
      final decoded = utf8.decode(base64Url.decode(normalized));
      final Map<String, dynamic> claims = jsonDecode(decoded);

      final possibleKeys = [
        'sub',
        'user_id',
        'userId',
        'id',
        'nameid',
        'unique_name'
      ];
      for (String key in possibleKeys) {
        if (claims.containsKey(key)) {
          return claims[key].toString();
        }
      }
    } catch (e) {
      log("Error decoding JWT token for trip: $e");
    }
    return null;
  }

  // Start a new trip
  Future<bool> startTrip({bool isKidMode = false}) async {
    try {
      if (_isTripActive) {
        log("🚗 Trip already active");
        return true;
      }

      log("🚗 Starting new trip (Kid Mode: $isKidMode)");

      // Get current location
      Position? currentPosition = await _getCurrentLocation();
      if (currentPosition == null) {
        log("❌ Could not get current location for trip start");
        return false;
      }

      _lastKnownPosition = currentPosition;

      // Get user ID
      final userId = _getUserId();
      if (userId == null) {
        log("❌ Could not get user ID for trip start");
        return false;
      }

      // Check if services are initialized
      if (_signalRService == null) {
        log("❌ TripService not properly initialized");
        return false;
      }

      // Start SignalR connection first to ensure traveler is connected
      try {
        await _signalRService!.startConnection();
        log("🔗 SignalR connection established before starting trip");
      } catch (e) {
        log("❌ Failed to establish SignalR connection: $e");
        // Continue anyway, the API might still work
      }

      // Start trip via StartTripCubit
      try {
        final startTripCubit = getIt<StartTripCubit>();
        await startTripCubit.startTrip(
          latitude: currentPosition.latitude,
          longitude: currentPosition.longitude,
          isKidMode: isKidMode,
        );

        // Listen to the cubit state
        final state = startTripCubit.state;
        if (state is StartTripSuccess) {
          _currentTripId = state.response.tripId;
          _isTripActive = true;

          // Start location tracking (every 20 seconds)
          _startLocationTracking();

          log("✅ Trip started successfully. Trip ID: $_currentTripId");
          return true;
        } else if (state is StartTripError) {
          log("❌ Failed to start trip: ${state.message}");

          // If the error is about traveler disconnection, provide helpful message
          if (state.message.contains("TRAVELER_DISCONNECTED") ||
              state.message.contains("must be connected")) {
            log("💡 Hint: Make sure you have supporters added and are connected to them");
          }

          return false;
        } else {
          log("❌ Failed to start trip: Unknown error");
          return false;
        }
      } catch (e) {
        log("❌ Error using StartTripCubit: $e");
        return false;
      }
    } catch (e) {
      log("❌ Error starting trip: $e");
      return false;
    }
  }

  // End current trip
  Future<bool> endTrip() async {
    try {
      if (!_isTripActive || _currentTripId == null) {
        log("🚗 No active trip to end");
        return true;
      }

      log("🚗 Ending trip: $_currentTripId");

      // Stop location tracking
      _stopLocationTracking();

      // Check if services are initialized
      if (_signalRService == null) {
        log("❌ TripService not properly initialized for end trip");
        return false;
      }

      // End trip via EndTripCubit
      try {
        final endTripCubit = getIt<EndTripCubit>();
        await endTripCubit.endTrip(_currentTripId!);

        // Listen to the cubit state
        final state = endTripCubit.state;
        if (state is EndTripSuccess) {
          _isTripActive = false;
          _currentTripId = null;
          _lastKnownPosition = null;

          // Stop SignalR connection
          await _signalRService!.stopConnection();

          log("✅ Trip ended successfully");
          return true;
        } else if (state is EndTripError) {
          log("❌ Failed to end trip: ${state.message}");
          return false;
        } else {
          log("❌ Failed to end trip: Unknown error");
          return false;
        }
      } catch (e) {
        log("❌ Error using EndTripCubit: $e");
        return false;
      }
    } catch (e) {
      log("❌ Error ending trip: $e");
      return false;
    }
  }

  // Start location tracking (every 20 seconds)
  void _startLocationTracking() {
    _stopLocationTracking(); // Stop any existing timer

    _locationTimer = Timer.periodic(const Duration(seconds: 20), (timer) async {
      await _updateLocation();
    });

    log("📍 Location tracking started (every 20 seconds)");
  }

  // Stop location tracking
  void _stopLocationTracking() {
    _locationTimer?.cancel();
    _locationTimer = null;
    log("📍 Location tracking stopped");
  }

  // Update current location
  Future<void> _updateLocation() async {
    try {
      if (!_isTripActive || _currentTripId == null) {
        log("📍 No active trip for location update");
        return;
      }

      // Get current location
      Position? currentPosition = await _getCurrentLocation();
      if (currentPosition == null) {
        log("📍 Could not get current location for update");
        return;
      }

      // Check if location changed significantly (at least 5 meters)
      if (_lastKnownPosition != null) {
        double distance = Geolocator.distanceBetween(
          _lastKnownPosition!.latitude,
          _lastKnownPosition!.longitude,
          currentPosition.latitude,
          currentPosition.longitude,
        );

        if (distance < 5.0) {
          log("📍 Location change too small (${distance.toStringAsFixed(1)}m), skipping update");
          return;
        }
      }

      _lastKnownPosition = currentPosition;

      // Check if services are initialized
      if (_signalRService == null) {
        log("❌ TripService not properly initialized for location update");
        return;
      }

      // Update location via UpdateLocationCubit
      try {
        final updateLocationCubit = getIt<UpdateLocationCubit>();
        await updateLocationCubit.updateLocation(
          tripId: _currentTripId!,
          latitude: currentPosition.latitude.toString(),
          longitude: currentPosition.longitude.toString(),
        );

        // Listen to the cubit state
        final state = updateLocationCubit.state;
        if (state is UpdateLocationSuccess) {
          // Send location via SignalR
          await _signalRService!.sendLocationUpdate({
            'tripId': _currentTripId,
            'latitude': currentPosition.latitude,
            'longitude': currentPosition.longitude,
            'timestamp': DateTime.now().toIso8601String(),
            'speed': currentPosition.speed,
            'heading': currentPosition.heading,
          });

          log("📍 Location updated: ${currentPosition.latitude.toStringAsFixed(6)}, ${currentPosition.longitude.toStringAsFixed(6)}");
        } else if (state is UpdateLocationError) {
          log("❌ Failed to update location: ${state.message}");
        } else {
          log("❌ Failed to update location: Unknown error");
        }
      } catch (e) {
        log("❌ Error using UpdateLocationCubit: $e");
      }
    } catch (e) {
      log("❌ Error updating location: $e");
    }
  }

  // Get current location
  Future<Position?> _getCurrentLocation() async {
    try {
      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        log("📍 Location services are disabled");
        return null;
      }

      // Check location permissions
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          log("📍 Location permissions are denied");
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        log("📍 Location permissions are permanently denied");
        return null;
      }

      // Get current position
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return position;
    } catch (e) {
      log("❌ Error getting current location: $e");
      return null;
    }
  }

  // Get trip status
  bool get isTripActive => _isTripActive;
  String? get currentTripId => _currentTripId;
  Position? get lastKnownPosition => _lastKnownPosition;

  // Dispose resources
  void dispose() {
    _stopLocationTracking();
    _isTripActive = false;
    _currentTripId = null;
    _lastKnownPosition = null;
  }
}
