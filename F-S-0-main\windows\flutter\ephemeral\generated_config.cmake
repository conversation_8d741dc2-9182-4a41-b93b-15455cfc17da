# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\src\\flutter\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "E:\\F-S-0-main\\F-S-0-main" PROJECT_DIR)

set(FLUTTER_VERSION "1.0.0+1" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 0 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 1 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\src\\flutter\\flutter"
  "PROJECT_DIR=E:\\F-S-0-main\\F-S-0-main"
  "FLUTTER_ROOT=C:\\src\\flutter\\flutter"
  "FLUTTER_EPHEMERAL_DIR=E:\\F-S-0-main\\F-S-0-main\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=E:\\F-S-0-main\\F-S-0-main"
  "FLUTTER_TARGET=E:\\F-S-0-main\\F-S-0-main\\lib\\main.dart"
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=E:\\F-S-0-main\\F-S-0-main\\.dart_tool\\package_config.json"
)
